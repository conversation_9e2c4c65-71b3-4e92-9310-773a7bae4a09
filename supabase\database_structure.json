// All Table Attributes
[
  {
    "table_name": "active_player_restrictions",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "table_name": "active_player_restrictions",
    "column_name": "player_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "table_name": "active_player_restrictions",
    "column_name": "restriction_type",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "active_player_restrictions",
    "column_name": "reason",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "active_player_restrictions",
    "column_name": "restriction_moderator_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "table_name": "active_player_restrictions",
    "column_name": "expires_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "active_player_restrictions",
    "column_name": "is_permanent",
    "data_type": "boolean",
    "is_nullable": "YES"
  },
  {
    "table_name": "active_player_restrictions",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "active_player_restrictions",
    "column_name": "moderation_action_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "table_name": "active_player_restrictions",
    "column_name": "player_name",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "active_player_restrictions",
    "column_name": "action_moderator_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "table_name": "active_player_restrictions",
    "column_name": "moderation_reason",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "battle_pass_seasons",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "battle_pass_seasons",
    "column_name": "season_name",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "battle_pass_seasons",
    "column_name": "start_date",
    "data_type": "timestamp with time zone",
    "is_nullable": "NO"
  },
  {
    "table_name": "battle_pass_seasons",
    "column_name": "end_date",
    "data_type": "timestamp with time zone",
    "is_nullable": "NO"
  },
  {
    "table_name": "battle_pass_seasons",
    "column_name": "is_active",
    "data_type": "boolean",
    "is_nullable": "YES"
  },
  {
    "table_name": "battle_pass_seasons",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "battle_pass_tiers",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "battle_pass_tiers",
    "column_name": "season_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "table_name": "battle_pass_tiers",
    "column_name": "tier_number",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "battle_pass_tiers",
    "column_name": "xp_required",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "battle_pass_tiers",
    "column_name": "free_reward",
    "data_type": "jsonb",
    "is_nullable": "YES"
  },
  {
    "table_name": "battle_pass_tiers",
    "column_name": "premium_reward",
    "data_type": "jsonb",
    "is_nullable": "YES"
  },
  {
    "table_name": "battle_pass_tiers",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "feedback",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "feedback",
    "column_name": "user_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "table_name": "feedback",
    "column_name": "category",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "feedback",
    "column_name": "title",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "feedback",
    "column_name": "description",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "feedback",
    "column_name": "rating",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "feedback",
    "column_name": "status",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "feedback",
    "column_name": "priority",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "feedback",
    "column_name": "browser_info",
    "data_type": "jsonb",
    "is_nullable": "YES"
  },
  {
    "table_name": "feedback",
    "column_name": "device_info",
    "data_type": "jsonb",
    "is_nullable": "YES"
  },
  {
    "table_name": "feedback",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "feedback",
    "column_name": "updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "feedback",
    "column_name": "admin_notes",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "feedback",
    "column_name": "resolved_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "feedback",
    "column_name": "moderator_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "table_name": "items",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "items",
    "column_name": "item_id",
    "data_type": "character varying",
    "is_nullable": "NO"
  },
  {
    "table_name": "items",
    "column_name": "name",
    "data_type": "character varying",
    "is_nullable": "NO"
  },
  {
    "table_name": "items",
    "column_name": "description",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "items",
    "column_name": "type",
    "data_type": "character varying",
    "is_nullable": "NO"
  },
  {
    "table_name": "items",
    "column_name": "rarity",
    "data_type": "character varying",
    "is_nullable": "YES"
  },
  {
    "table_name": "items",
    "column_name": "image_url",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "items",
    "column_name": "unlock_requirement",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "items",
    "column_name": "is_premium",
    "data_type": "boolean",
    "is_nullable": "YES"
  },
  {
    "table_name": "items",
    "column_name": "sort_order",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "items",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "NO"
  },
  {
    "table_name": "items",
    "column_name": "updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "NO"
  },
  {
    "table_name": "leaderboard_view",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "table_name": "leaderboard_view",
    "column_name": "display_name",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "leaderboard_view",
    "column_name": "avatar_url",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "leaderboard_view",
    "column_name": "avatar_border",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "leaderboard_view",
    "column_name": "background_display",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "leaderboard_view",
    "column_name": "rankpoints",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "leaderboard_view",
    "column_name": "highest_rankpoints",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "leaderboard_view",
    "column_name": "current_rank_tier",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "leaderboard_view",
    "column_name": "current_rank_division",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "leaderboard_view",
    "column_name": "rank_updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "leaderboard_view",
    "column_name": "tier_order",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "leaderboard_view",
    "column_name": "division_order",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_histories",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_histories",
    "column_name": "difficulty",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_histories",
    "column_name": "current_round",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_histories",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_histories",
    "column_name": "match_duration_seconds",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_histories",
    "column_name": "player_ids",
    "data_type": "jsonb",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_histories",
    "column_name": "ended_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_histories",
    "column_name": "is_valid",
    "data_type": "boolean",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_history_players",
    "column_name": "match_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_history_players",
    "column_name": "player_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_history_players",
    "column_name": "score",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_history_players",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_history_players",
    "column_name": "final_rank",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_history_players",
    "column_name": "elimination_round",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_history_players",
    "column_name": "difficulty",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_history_players",
    "column_name": "rp_gained",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_history_players",
    "column_name": "rp_before_match",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_history_players",
    "column_name": "rp_after_match",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_history_players",
    "column_name": "rank_before_match",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_history_players",
    "column_name": "rank_after_match",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_players",
    "column_name": "match_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_players",
    "column_name": "player_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_players",
    "column_name": "score",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_players",
    "column_name": "lives",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_players",
    "column_name": "is_spectator",
    "data_type": "boolean",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_players",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_players",
    "column_name": "has_answered",
    "data_type": "boolean",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_players",
    "column_name": "lastAnswer",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_players",
    "column_name": "longest_streak",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_players",
    "column_name": "current_streak",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_players",
    "column_name": "correct_answers",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_players",
    "column_name": "total_answers",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_players",
    "column_name": "status",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_players",
    "column_name": "left_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_players",
    "column_name": "response_times",
    "data_type": "ARRAY",
    "is_nullable": "YES"
  },
  {
    "table_name": "matches",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "matches",
    "column_name": "difficulty",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "matches",
    "column_name": "status",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "matches",
    "column_name": "current_round",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "matches",
    "column_name": "current_state",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "matches",
    "column_name": "current_word_id",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "matches",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "matches",
    "column_name": "waiting_time",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "matches",
    "column_name": "start_time",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "matches",
    "column_name": "available_word_ids",
    "data_type": "jsonb",
    "is_nullable": "YES"
  },
  {
    "table_name": "matches",
    "column_name": "is_locked",
    "data_type": "boolean",
    "is_nullable": "NO"
  },
  {
    "table_name": "moderation_actions",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "moderation_actions",
    "column_name": "player_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "moderation_actions",
    "column_name": "moderator_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "moderation_actions",
    "column_name": "action_type",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "moderation_actions",
    "column_name": "reason",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "moderation_actions",
    "column_name": "duration_hours",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "moderation_actions",
    "column_name": "report_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "table_name": "moderation_actions",
    "column_name": "is_active",
    "data_type": "boolean",
    "is_nullable": "YES"
  },
  {
    "table_name": "moderation_actions",
    "column_name": "expires_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "moderation_actions",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "moderation_actions",
    "column_name": "notes",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_battle_pass",
    "column_name": "player_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "player_battle_pass",
    "column_name": "season_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "player_battle_pass",
    "column_name": "current_tier",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_battle_pass",
    "column_name": "current_xp",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_battle_pass",
    "column_name": "has_premium",
    "data_type": "boolean",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_battle_pass",
    "column_name": "last_updated",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_item_inventory",
    "column_name": "player_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "player_item_inventory",
    "column_name": "item_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "player_item_inventory",
    "column_name": "unlocked_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_items",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "player_items",
    "column_name": "name",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "player_items",
    "column_name": "type",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "player_items",
    "column_name": "rarity",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "player_items",
    "column_name": "value",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "player_items",
    "column_name": "unlock_requirement",
    "data_type": "jsonb",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_items",
    "column_name": "is_default",
    "data_type": "boolean",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_items",
    "column_name": "description",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_items",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_items",
    "column_name": "image_url",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_items",
    "column_name": "is_animated",
    "data_type": "boolean",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_reports",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "player_reports",
    "column_name": "reporter_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "player_reports",
    "column_name": "reported_player_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "player_reports",
    "column_name": "category",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "player_reports",
    "column_name": "reason",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "player_reports",
    "column_name": "additional_details",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_reports",
    "column_name": "status",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_reports",
    "column_name": "moderator_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_reports",
    "column_name": "moderator_notes",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_reports",
    "column_name": "resolved_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_reports",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_reports",
    "column_name": "updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_restrictions",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "player_restrictions",
    "column_name": "player_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "player_restrictions",
    "column_name": "restriction_type",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "player_restrictions",
    "column_name": "reason",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "player_restrictions",
    "column_name": "moderator_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_restrictions",
    "column_name": "expires_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_restrictions",
    "column_name": "is_permanent",
    "data_type": "boolean",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_restrictions",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "player_restrictions",
    "column_name": "moderation_action_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "table_name": "players",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "players",
    "column_name": "avatar_url",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "players",
    "column_name": "stats",
    "data_type": "jsonb",
    "is_nullable": "YES"
  },
  {
    "table_name": "players",
    "column_name": "display_name",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "players",
    "column_name": "title",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "players",
    "column_name": "background_display",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "players",
    "column_name": "avatar_border",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "players",
    "column_name": "level_experience",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "players",
    "column_name": "privacy_settings",
    "data_type": "jsonb",
    "is_nullable": "YES"
  },
  {
    "table_name": "players",
    "column_name": "rankpoints",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "players",
    "column_name": "highest_rankpoints",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "players",
    "column_name": "current_rank_tier",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "players",
    "column_name": "current_rank_division",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "players",
    "column_name": "rank_updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "players",
    "column_name": "equipped_avatar_border_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "table_name": "players",
    "column_name": "equipped_background_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "table_name": "players",
    "column_name": "equipped_title_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "table_name": "players",
    "column_name": "equipped_avatar_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "table_name": "rank_history",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "rank_history",
    "column_name": "player_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "rank_history",
    "column_name": "match_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "table_name": "rank_history",
    "column_name": "rp_change",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "rank_history",
    "column_name": "rp_before",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "rank_history",
    "column_name": "rp_after",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "rank_history",
    "column_name": "rank_before",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "rank_history",
    "column_name": "rank_after",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "rank_history",
    "column_name": "reason",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "rank_history",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "report_categories",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "report_categories",
    "column_name": "category_id",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "report_categories",
    "column_name": "name",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "report_categories",
    "column_name": "description",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "report_categories",
    "column_name": "is_active",
    "data_type": "boolean",
    "is_nullable": "YES"
  },
  {
    "table_name": "report_categories",
    "column_name": "severity_level",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "report_categories",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "system_settings",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "system_settings",
    "column_name": "setting_key",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "system_settings",
    "column_name": "setting_value",
    "data_type": "jsonb",
    "is_nullable": "NO"
  },
  {
    "table_name": "system_settings",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "system_settings",
    "column_name": "updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "user_stats",
    "column_name": "user_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "highest_round_reached",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "correct_answers",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "total_answers",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "matches_played",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "difficulty",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "longest_streak",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "last_weekly_reset",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "user_stats",
    "column_name": "weekly_rank",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "user_stats",
    "column_name": "previous_weekly_score",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "user_stats",
    "column_name": "rankpoints",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "highest_rankpoints",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "current_rank_tier",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "current_rank_division",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "rank_updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "words",
    "column_name": "id",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "words",
    "column_name": "word",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "words",
    "column_name": "difficulty",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "words",
    "column_name": "audio_clip_url",
    "data_type": "text",
    "is_nullable": "YES"
  }
]


// Foreign Key Relationships
[
  {
    "source_table": "battle_pass_tiers",
    "source_column": "season_id",
    "target_table": "battle_pass_seasons",
    "target_column": "id"
  },
  {
    "source_table": "match_history_players",
    "source_column": "player_id",
    "target_table": "players",
    "target_column": "id"
  },
  {
    "source_table": "match_history_players",
    "source_column": "match_id",
    "target_table": "match_histories",
    "target_column": "id"
  },
  {
    "source_table": "match_players",
    "source_column": "match_id",
    "target_table": "matches",
    "target_column": "id"
  },
  {
    "source_table": "match_players",
    "source_column": "player_id",
    "target_table": "players",
    "target_column": "id"
  },
  {
    "source_table": "moderation_actions",
    "source_column": "report_id",
    "target_table": "player_reports",
    "target_column": "id"
  },
  {
    "source_table": "moderation_actions",
    "source_column": "player_id",
    "target_table": "players",
    "target_column": "id"
  },
  {
    "source_table": "player_battle_pass",
    "source_column": "player_id",
    "target_table": "players",
    "target_column": "id"
  },
  {
    "source_table": "player_battle_pass",
    "source_column": "season_id",
    "target_table": "battle_pass_seasons",
    "target_column": "id"
  },
  {
    "source_table": "player_item_inventory",
    "source_column": "item_id",
    "target_table": "player_items",
    "target_column": "id"
  },
  {
    "source_table": "player_item_inventory",
    "source_column": "player_id",
    "target_table": "players",
    "target_column": "id"
  },
  {
    "source_table": "player_reports",
    "source_column": "reported_player_id",
    "target_table": "players",
    "target_column": "id"
  },
  {
    "source_table": "player_restrictions",
    "source_column": "moderation_action_id",
    "target_table": "moderation_actions",
    "target_column": "id"
  },
  {
    "source_table": "player_restrictions",
    "source_column": "player_id",
    "target_table": "players",
    "target_column": "id"
  },
  {
    "source_table": "players",
    "source_column": "equipped_background_id",
    "target_table": "player_items",
    "target_column": "id"
  },
  {
    "source_table": "players",
    "source_column": "equipped_title_id",
    "target_table": "player_items",
    "target_column": "id"
  },
  {
    "source_table": "players",
    "source_column": "equipped_avatar_border_id",
    "target_table": "player_items",
    "target_column": "id"
  },
  {
    "source_table": "players",
    "source_column": "equipped_avatar_id",
    "target_table": "player_items",
    "target_column": "id"
  },
  {
    "source_table": "rank_history",
    "source_column": "match_id",
    "target_table": "match_histories",
    "target_column": "id"
  },
  {
    "source_table": "rank_history",
    "source_column": "player_id",
    "target_table": "players",
    "target_column": "id"
  },
  {
    "source_table": "user_stats",
    "source_column": "user_id",
    "target_table": "players",
    "target_column": "id"
  }
]
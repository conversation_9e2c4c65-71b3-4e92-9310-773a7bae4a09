{"private": true, "scripts": {"dev": "next dev", "build": "next build --turbopack", "start": "next start"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.3", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@types/howler": "^2.2.12", "@types/uuid": "^10.0.0", "autoprefixer": "10.4.20", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.5.0", "howler": "^2.2.4", "lucide-react": "^0.468.0", "next": "^15.4.4", "next-themes": "^0.2.1", "prettier": "^3.3.3", "radix-ui": "^1.1.3", "react": "^19.1.0", "react-day-picker": "^9.5.1", "react-detect-offline": "^2.4.5", "react-dom": "^19.1.0", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "sonner": "^2.0.3", "stripe": "^17.6.0", "tempo-devtools": "^2.0.102", "use-sound": "^5.0.0", "uuid": "^11.1.0", "vaul": "^1.1.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8", "tailwind-merge": "^2", "tailwindcss": "^3", "tailwindcss-animate": "^1", "typescript": "^5"}}
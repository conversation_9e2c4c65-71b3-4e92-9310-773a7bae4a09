"use client";

import { createClient } from "../../supabase/client";
import { toast } from "sonner";
import {
  getCurrentWord,
  setCurrentWordId,
  setMatchLock,
  updateMatchCurrentState,
} from "./database";
import { currentWord, Difficulty, Player } from "@/interfaces/interfaces";
import { calculateTimeLeft, resetStartTime } from "./waiting-utils";

// Constants
const NO_ROWS_RETURNED_ERROR_CODE = "PGRST116";

const supabase = createClient();

export const useSupabase = () => {
  return supabase;
};

interface Word {
  text: string;
  difficulty: Difficulty;
}

// Mock data for demonstration
export const players: Player[] = [
  {
    id: "1",
    display_name: "Player One",
    avatar_url: "/path/to/avatar1.png",
    lives: 3,
    score: 120,
    is_spectator: false,
    has_answered: false,
    lastAnswer: "correct",
  },
  {
    id: "2",
    display_name: "Player Two",
    avatar_url: "/path/to/avatar2.png",
    lives: 2,
    score: 80,
    is_spectator: false,
    has_answered: false,
    lastAnswer: "incorrect",
  },
  {
    id: "3",
    display_name: "Spectator",
    avatar_url: "/path/to/avatar3.png",
    lives: 0,
    score: 40,
    is_spectator: true,
    has_answered: false,
    lastAnswer: "pending",
  },
];

export const timeLeft = 15;
export const roundNumber = 1;
export const isAudioPlaying = false;

export const getActivePlayers = () => {
  return players.filter(
    (player) => (player.lives || 0) > 0 && !player.is_spectator
  );
};

export const getEliminatedPlayers = () => {
  return players.filter(
    (player) => (player.lives || 0) <= 0 || player.is_spectator
  );
};

export const fetchPlayersFromDatabase = async (
  roomName: string,
  matchId: string,
  includeLeftPlayers: boolean = false
): Promise<Player[]> => {
  try {
    // fetch all players in this match with their player profile data
    // By default, only fetch active players. Set includeLeftPlayers=true to get all players including those who left
    let query = supabase
      .from("match_players")
      .select(
        `
        player_id,
        score,
        lastAnswer,
        lives,
        has_answered,
        is_spectator,
        longest_streak,
        current_streak,
        correct_answers,
        total_answers,
        response_times,
        status,
        left_at,
        players (
          id,
          display_name,
          avatar_url,
          equipped_avatar_border_id,
          avatar_border_item:equipped_avatar_border_id(image_url),
          equipped_avatar_id,
          avatar_items:equipped_avatar_id(image_url)
        )
      `
      )
      .eq("match_id", matchId);

    // Filter by status if we don't want to include players who left
    if (!includeLeftPlayers) {
      query = query.neq("status", "left");
    }

    const { data: matchPlayersData, error: matchPlayersError } = await query;

    if (matchPlayersError) {
      toast.error("Failed to fetch match players.");
      console.error("Error fetching match players:", matchPlayersError);
      return [];
    }
    if (matchPlayersData && matchPlayersData.length > 0) {
      // convert database format to our player interface
      const formattedPlayers: Player[] = matchPlayersData.map(
        (matchPlayer: any) => {
          const playerProfile = matchPlayer.players;
          return {
            id: matchPlayer.player_id,
            display_name: playerProfile.display_name || "Anonymous",
            avatar_url:
              playerProfile?.avatar_items?.image_url ||
              playerProfile?.avatar_url,
            avatar_border: playerProfile?.avatar_border_item?.image_url,
            lives: matchPlayer.lives,
            score: matchPlayer.score,
            is_spectator: matchPlayer.is_spectator,
            has_answered: matchPlayer.has_answered,
            lastAnswer: matchPlayer.lastAnswer,
            longest_streak: matchPlayer.longest_streak || 0,
            current_streak: matchPlayer.current_streak || 0,
            total_answers: matchPlayer.total_answers || 0,
            correct_answers: matchPlayer.correct_answers || 0,
            responseTimes: matchPlayer.response_times || [],
            status: matchPlayer.status,
            left_at: matchPlayer.left_at,
          };
        }
      );

      return formattedPlayers;
    }

    // Return empty array if no players found
    return [];
  } catch (error) {
    console.error("Error fetching players:", error);
    return [];
  }
};

export const getCurrentWordForMatch = async (
  matchId?: string
): Promise<currentWord> => {
  if (matchId) {
    const wordData = await getCurrentWord(matchId);
    if (wordData) {
      return {
        id: wordData.id,
        text: wordData.word,
        difficulty: wordData.difficulty,
        audioUrl: wordData.audio_clip_url || undefined,
      };
    }
  }

  // Fallback to mock data if no match ID or word data not found
  return {
    id: 0,
    text: "",
    difficulty: "easy",
  };
};

export async function initiateSpellingTimer(
  matchId: string,
  setTimeLeft: (time: number) => void,
  setTimeLeftPrecise: (time: number) => void,
  setInitialTime: (time: number) => void,
  initialTimeInSeconds: number
) {
  try {
    const { data: match, error: matchError } = await supabase
      .from("matches")
      .select("start_time, current_state")
      .eq("id", matchId)
      .single();

    if (matchError) {
      console.error("Error fetching waiting users:", matchError);
    }

    const currentStartTime = match?.start_time;

    if (!currentStartTime) {
      const success = await attemptSetStartTime(matchId, initialTimeInSeconds);
      if (success) {
        setTimeLeft(calculateTimeLeft(success));
        setTimeLeftPrecise(calculateTimeLeft(success) * 1000);
        setInitialTime(initialTimeInSeconds);
      }
    } else if (currentStartTime) {
      setTimeLeftPrecise(calculateTimeLeft(currentStartTime) * 1000);
      setInitialTime(initialTimeInSeconds);
    }
  } catch (error) {
    console.error("Exception fetching waiting users:", error);
  }
}

async function attemptSetStartTime(
  matchId: string,
  initialTimeInSeconds: number
) {
  try {
    const { data: currentMatch, error: fetchError } = await supabase
      .from("matches")
      .select("start_time")
      .eq("id", matchId)
      .single();

    if (fetchError || !currentMatch) {
      console.error("Error fetching current match:", fetchError);
      return false;
    }

    const startTime = new Date();
    startTime.setSeconds(startTime.getSeconds() + initialTimeInSeconds);
    const startTimeISO = startTime.toISOString();

    const { error: updateError } = await supabase
      .from("matches")
      .update({ start_time: startTimeISO })
      .eq("id", matchId)
      .is("start_time", null)
      .select();

    if (updateError) {
      console.error("Error setting start time:", updateError);
      return false;
    }

    return startTimeISO;
  } catch (error) {
    console.error("Exception setting start time:", error);
    return false;
  }
}

export async function performHeartbeatSyncBattle(
  supabase: ReturnType<typeof createClient>,
  currentMatchId: string | null,
  setTimeLeft: (time: number) => void,
  setTimeLeftPrecise: (time: number) => void,
  isAudioPlaying: boolean,
  clientGameState: string,
  breakTimerStart: boolean
) {
  if (isAudioPlaying && clientGameState == "spelling") {
    console.log("Can't perform heartbeat");
    return;
  }

  if (!breakTimerStart && clientGameState == "break") {
    console.log("Can't perform heartbeat");
    return;
  }

  try {
    if (!currentMatchId) return;
    const { data: currentMatch, error } = await supabase
      .from("matches")
      .select("id, status, start_time")
      .eq("id", currentMatchId)
      .single();

    if (error || !currentMatch) {
      return;
    }

    const now = new Date();
    const startTime = currentMatch.start_time
      ? new Date(currentMatch.start_time)
      : null;

    if (startTime) {
      const newTimeLeft = Math.max(
        0,
        Math.floor((startTime.getTime() - now.getTime()) / 1000)
      );
      setTimeLeft(newTimeLeft);
      setTimeLeftPrecise(newTimeLeft * 1000);
    }
  } catch (error) {
    console.error("Exception in heartbeat sync:", error);
  }
}

export async function updatePendingPlayers(matchId: string) {
  try {
    const { data: matchPlayers, error } = await supabase
      .from("match_players")
      .select("player_id, lastAnswer, lives, has_answered")
      .eq("match_id", matchId);

    if (error) {
      console.error("Error fetching match players for update:", error);
      return;
    }

    if (matchPlayers && matchPlayers.length > 0) {
      const updates = matchPlayers
        .filter((player) => player.lastAnswer === "pending")
        .map((player) => ({
          match_id: matchId,
          player_id: player.player_id,
          lastAnswer: "incorrect",
          current_streak: 0,
          lives: (player.lives || 0) - 1,
        }));

      if (updates.length > 0) {
        const { error: updateError } = await supabase
          .from("match_players")
          .upsert(updates, { onConflict: "match_id,player_id" });

        if (updateError) {
          console.error("Error updating pending players:", updateError);
        }
      }
    }
  } catch (error) {
    console.error("Exception in updatePendingPlayers:", error);
  }
}

export async function updatePlayersHasAnswered(matchId: string) {
  const { error } = await supabase
    .from("match_players")
    .update({ has_answered: false })
    .eq("match_id", matchId);

  if (error) {
    console.error(error);
  }
}

export async function updateAllPlayersToPending(matchId: string) {
  const { data, error: matchesError } = await supabase
    .from("matches")
    .select("is_locked")
    .eq("id", matchId)
    .single();

  if (matchesError) {
    console.error(matchesError);
  }

  if (data?.is_locked) {
    const { error: playersError } = await supabase
      .from("match_players")
      .update({ lastAnswer: "pending" })
      .eq("match_id", matchId)
      .neq("status", "left"); // Only update active players

    if (playersError) {
      console.error(playersError);
    }
  }
}

/**
 * Soft delete a player from a match by updating their status to 'left'
 * This preserves their data for match history while removing them from active gameplay
 */
export async function softLeaveMatch(matchId: string, playerId: string) {
  try {
    const { error } = await supabase
      .from("match_players")
      .update({
        status: "left",
        left_at: new Date().toISOString(),
      })
      .eq("match_id", matchId)
      .eq("player_id", playerId);

    if (error) {
      console.error("Error soft leaving match:", error);
      return false;
    }

    console.log(`Player ${playerId} soft left match ${matchId}`);
    return true;
  } catch (error) {
    console.error("Exception in softLeaveMatch:", error);
    return false;
  }
}

/**
 * Get all players for a match including those who left (for match history)
 */
export async function getAllMatchPlayers(matchId: string) {
  try {
    const { data, error } = await supabase
      .from("match_players")
      .select(
        `
        player_id,
        score,
        lives,
        longest_streak,
        current_streak,
        correct_answers,
        total_answers,
        status,
        left_at,
        players (
          id,
          display_name,
          avatar_url
        )
      `
      )
      .eq("match_id", matchId)
      .order("score", { ascending: false }); // Order by score for ranking

    if (error) {
      console.error("Error fetching all match players:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Exception in getAllMatchPlayers:", error);
    return [];
  }
}

/**
 * Save match to history with all player data (including those who left)
 * This replaces the need for the move_match_to_history database function
 */
export async function saveMatchToHistory(matchId: string) {
  try {
    // Check first if the match is already in the match_histories table
    const { data: existingHistory, error: historyCheckError } = await supabase
      .from("match_histories")
      .select("*")
      .eq("id", matchId)
      .single();

    // If the match is already in the history, return true
    if (historyCheckError?.code === NO_ROWS_RETURNED_ERROR_CODE) {
      // Get match data
      const { data: matchData, error: matchError } = await supabase
        .from("matches")
        .select("*")
        .eq("id", matchId)
        .single();

      if (matchError || !matchData) {
        console.error("Error fetching match data:", matchError);
        return false;
      }

      // Validate match: Must have reached at least round 2
      const isValidMatch = matchData.current_round >= 4;
      if (!isValidMatch) {
        console.log(
          `Match ${matchId} is invalid (round ${matchData.current_round} < 2). No rewards will be given.`
        );
      }

      // Get all players (including those who left)
      const allPlayers = await getAllMatchPlayers(matchId);

      if (allPlayers.length === 0) {
        console.error("No players found for match");
        return false;
      }

      // Calculate match duration
      const startTime = matchData.start_time
        ? new Date(matchData.start_time)
        : new Date(matchData.created_at);
      const endTime = new Date();
      const durationSeconds = Math.floor(
        (endTime.getTime() - startTime.getTime()) / 1000
      );

      // Create player_ids array for match_histories
      const playerIds = allPlayers.map((p) => p.player_id); // Insert into match_histories
      const { error: historyError } = await supabase
        .from("match_histories")
        .insert({
          id: matchId, // Use same ID as the original match
          difficulty: matchData.difficulty,
          current_round: matchData.current_round,
          match_duration_seconds: durationSeconds,
          player_ids: playerIds,
          created_at: matchData.created_at,
          ended_at: endTime.toISOString(),
          is_valid: isValidMatch, // Add validity flag
        })
        .select()
        .single();

      if (historyError) {
        console.error("Error inserting match history:", historyError);
        return false;
      }

      // Calculate average score for performance bonus calculation
      const totalScore = allPlayers.reduce(
        (sum, player) => sum + (player.score || 0),
        0
      );
      const averageScore = totalScore / allPlayers.length;

      // Insert individual player records into match_history_players with RP tracking
      const playerHistoryRecords = [];

      for (let index = 0; index < allPlayers.length; index++) {
        const player = allPlayers[index];

        // Get current player RP before match
        const { data: playerData } = await supabase
          .from("players")
          .select("rankpoints")
          .eq("id", player.player_id)
          .single();

        const currentRP = playerData?.rankpoints || 0; // Calculate RP change for this player
        const { calculateRPChange } = await import("./ranking-system");
        const rpChange = isValidMatch
          ? calculateRPChange(
              currentRP,
              index + 1, // final rank
              allPlayers.length,
              durationSeconds / 60, // duration in minutes
              matchData.difficulty,
              player.score || 0,
              averageScore
            )
          : { totalRP: 0 }; // No RP for invalid matches

        playerHistoryRecords.push({
          match_id: matchId,
          player_id: player.player_id,
          score: player.score || 0,
          final_rank: index + 1, // Rank based on score order
          elimination_round: player.lives <= 0 ? matchData.current_round : null,
          difficulty: matchData.difficulty,
          rp_gained: rpChange.totalRP,
          rp_before_match: currentRP,
          rp_after_match: Math.max(0, currentRP + rpChange.totalRP),
          rank_before_match: "", // Will be filled by ranking system
          rank_after_match: "", // Will be filled by ranking system
        });
      }

      const { error: playersHistoryError } = await supabase
        .from("match_history_players")
        .insert(playerHistoryRecords);

      if (playersHistoryError) {
        console.error(
          "Error inserting player history records:",
          playersHistoryError
        );
        return false;
      } // Award RP to all players based on their performance - only for valid matches
      if (isValidMatch) {
        const { updatePlayerRP } = await import("./ranking-system");

        for (const record of playerHistoryRecords) {
          try {
            const success = await updatePlayerRP(
              record.player_id,
              record.rp_gained,
              matchId,
              "match_result"
            );

            if (success) {
              console.log(
                `Awarded ${record.rp_gained > 0 ? "+" : ""}${record.rp_gained} RP to player ${record.player_id} (Rank ${record.final_rank})`
              );
            } else {
              console.error(`Failed to award RP to player ${record.player_id}`);
            }
          } catch (rpError) {
            console.error(
              `Error awarding RP to player ${record.player_id}:`,
              rpError
            );
            // Don't fail the entire function if RP award fails
          }
        }
      } else {
        console.log("Match was invalid - no RP awarded to any players");
      }

      // Now clean up: delete the original match and all match_players records
      const { error: deletePlayersError } = await supabase
        .from("match_players")
        .delete()
        .eq("match_id", matchId);

      if (deletePlayersError) {
        console.error("Error deleting match players:", deletePlayersError);
        // Don't return false here, history is already saved
      }

      const { error: deleteMatchError } = await supabase
        .from("matches")
        .delete()
        .eq("id", matchId);

      if (deleteMatchError) {
        console.error("Error deleting match:", deleteMatchError);
        // Don't return false here, history is already saved
      }

      console.log(
        `Match ${matchId} successfully moved to history with ${allPlayers.length} players`
      );
      return true;
    }

    if (existingHistory) {
      console.log("Match already in history, skipping");
      return true;
    }
  } catch (error) {
    console.error("Exception in saveMatchToHistory:", error);
    return false;
  }
}

export const transitionToBreak = async (
  matchId: string,
  setTimeLeft: (time: number) => void,
  setTimeLeftPrecise: (time: number) => void,
  setInitialTime: (time: number) => void,
  initialBreakTimeInSeconds: number,
  clientCurrentWordId: number,
  setCurrentRound: (round: number) => void,
  setIsAudioPlaying: (isPlaying: boolean) => void,
  setBreakTimerStart: (isStarted: boolean) => void,
  clearTimer: () => void,
  shouldEndMatch: () => boolean,
  endMatch: () => Promise<void>,
  currentRound: number,
) => {
  if (!matchId) return;
  clearTimer();

  if (shouldEndMatch()) {
    await endMatch();
  }

  const { error } = await supabase
    .from("matches")
    .update({ all_player_answered: false })
    .eq("id", matchId)
    .eq("all_player_answered", true)
    .single();

  if (error) {
    console.log("Some updated all_player_answered:", false);
  } else {
    console.log("All players have answered - match state updated");
  }

  await updateMatchCurrentState(matchId, "spelling", "break");
  await updatePendingPlayers(matchId); // Update all players pending to incorrect
  await resetStartTime(matchId);

  await initiateSpellingTimer(
    matchId,
    setTimeLeft,
    setTimeLeftPrecise,
    setInitialTime,
    initialBreakTimeInSeconds
  );

  const pickedWordId = await setCurrentWordId(matchId, clientCurrentWordId);
  console.log(`Picked ID: ${pickedWordId}`);

  // Increment round number in database
  const { error: roundError } = await supabase
    .from("matches")
    .update({ current_round: currentRound + 1 })
    .eq("id", matchId);

  if (!roundError) {
    setCurrentRound(currentRound + 1);
  }

  setIsAudioPlaying(true);
  setBreakTimerStart(true);
};

export const transitionToSpelling = async (
  matchId: string,
  setBreakTimerStart: (isStarted: boolean) => void,
  playAudio: () => Promise<currentWord>,
  clearTimer: () => void,
  shouldEndMatch: () => boolean,
  endMatch: () => Promise<void>,
  playerData: Player[],
  setPlayerData: (data: Player[]) => void
) => {
  if (!matchId) return;
  clearTimer();

  if (shouldEndMatch()) {
    await endMatch();
  }

  // Reset has_answered for all players in local state
  setPlayerData(playerData.map((p) => ({ ...p, has_answered: false })));
  await updatePlayersHasAnswered(matchId); // Reset has_answered for all players
  await resetStartTime(matchId);
  await updateMatchCurrentState(matchId, "break", "spelling");

  await updateAllPlayersToPending(matchId); // Update all players to pending

  await setMatchLock(matchId, false);
  setBreakTimerStart(false);
  await playAudio();
};
